"""
Tool Usage Service - Track and manage tool usage across conversations
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pymongo.collection import Collection
from bson import ObjectId

from core.database import get_db_from_tenant_id
from models.tool_usage import (
    ToolUsageModel, ToolUsageCreate, ToolUsageResponse, 
    ToolUsageStats, ConversationToolUsage
)

logger = logging.getLogger(__name__)


class ToolUsageService:
    """Service for managing tool usage tracking"""
    
    def __init__(self, tenant_id: str):
        """Initialize tool usage service"""
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection: Collection = self.db.tool_usage
        
        # Create indexes for better performance
        self._create_indexes()
        
    def _create_indexes(self):
        """Create database indexes for tool usage collection"""
        try:
            # Index for user and session queries
            self.collection.create_index([("user_id", 1), ("session_id", 1)])
            # Index for timestamp queries
            self.collection.create_index([("timestamp", -1)])
            # Index for tool name queries
            self.collection.create_index([("tool_name", 1)])
            # Compound index for analytics
            self.collection.create_index([("tenant_id", 1), ("timestamp", -1)])
            
            logger.info("✅ Tool usage indexes created")
        except Exception as e:
            logger.warning(f"⚠️ Could not create tool usage indexes: {e}")
    
    async def log_tool_usage(
        self, 
        tool_usage: ToolUsageCreate, 
        user_id: str, 
        session_id: str
    ) -> ToolUsageResponse:
        """Log a tool usage event"""
        try:
            # Create tool usage document
            usage_doc = {
                "tool_name": tool_usage.tool_name,
                "tool_description": tool_usage.tool_description,
                "input_data": tool_usage.input_data,
                "output_data": tool_usage.output_data,
                "execution_time_ms": tool_usage.execution_time_ms,
                "success": tool_usage.success,
                "error_message": tool_usage.error_message,
                "timestamp": datetime.now(),
                "user_id": user_id,
                "session_id": session_id,
                "tenant_id": self.tenant_id,
                "conversation_context": tool_usage.conversation_context
            }
            
            # Insert into database
            result = self.collection.insert_one(usage_doc)
            usage_id = str(result.inserted_id)
            
            logger.info(f"✅ Tool usage logged: {tool_usage.tool_name} for user {user_id}")
            
            return ToolUsageResponse(
                id=usage_id,
                tool_name=tool_usage.tool_name,
                tool_description=tool_usage.tool_description,
                input_data=tool_usage.input_data,
                output_data=tool_usage.output_data,
                execution_time_ms=tool_usage.execution_time_ms,
                success=tool_usage.success,
                error_message=tool_usage.error_message,
                timestamp=usage_doc["timestamp"],
                conversation_context=tool_usage.conversation_context
            )
            
        except Exception as e:
            logger.error(f"❌ Error logging tool usage: {e}")
            raise
    
    async def get_user_tool_usage(
        self, 
        user_id: str, 
        session_id: Optional[str] = None,
        limit: int = 50
    ) -> List[ToolUsageResponse]:
        """Get tool usage for a specific user"""
        try:
            query = {"user_id": user_id, "tenant_id": self.tenant_id}
            if session_id:
                query["session_id"] = session_id
            
            cursor = self.collection.find(query).sort("timestamp", -1).limit(limit)
            usage_records = []
            
            for record in cursor:
                usage_records.append(ToolUsageResponse(
                    id=str(record["_id"]),
                    tool_name=record["tool_name"],
                    tool_description=record["tool_description"],
                    input_data=record.get("input_data", {}),
                    output_data=record.get("output_data", ""),
                    execution_time_ms=record.get("execution_time_ms"),
                    success=record.get("success", True),
                    error_message=record.get("error_message"),
                    timestamp=record["timestamp"],
                    conversation_context=record.get("conversation_context")
                ))
            
            logger.info(f"📊 Retrieved {len(usage_records)} tool usage records for user {user_id}")
            return usage_records
            
        except Exception as e:
            logger.error(f"❌ Error getting user tool usage: {e}")
            return []
    
    async def get_conversation_tool_usage(self, session_id: str) -> ConversationToolUsage:
        """Get tool usage for a specific conversation"""
        try:
            query = {"session_id": session_id, "tenant_id": self.tenant_id}
            cursor = self.collection.find(query).sort("timestamp", 1)
            
            tool_calls = []
            timestamps = []
            
            for record in cursor:
                tool_calls.append(ToolUsageResponse(
                    id=str(record["_id"]),
                    tool_name=record["tool_name"],
                    tool_description=record["tool_description"],
                    input_data=record.get("input_data", {}),
                    output_data=record.get("output_data", ""),
                    execution_time_ms=record.get("execution_time_ms"),
                    success=record.get("success", True),
                    error_message=record.get("error_message"),
                    timestamp=record["timestamp"],
                    conversation_context=record.get("conversation_context")
                ))
                timestamps.append(record["timestamp"])
            
            if not tool_calls:
                # Return empty conversation
                return ConversationToolUsage(
                    session_id=session_id,
                    user_id="",
                    message_count=0,
                    tool_calls=[],
                    first_message=datetime.now(),
                    last_message=datetime.now()
                )
            
            return ConversationToolUsage(
                session_id=session_id,
                user_id=tool_calls[0].id if tool_calls else "",
                message_count=len(tool_calls),
                tool_calls=tool_calls,
                first_message=min(timestamps),
                last_message=max(timestamps)
            )
            
        except Exception as e:
            logger.error(f"❌ Error getting conversation tool usage: {e}")
            return ConversationToolUsage(
                session_id=session_id,
                user_id="",
                message_count=0,
                tool_calls=[],
                first_message=datetime.now(),
                last_message=datetime.now()
            )
    
    async def get_tool_usage_stats(
        self, 
        user_id: Optional[str] = None,
        days: int = 30
    ) -> ToolUsageStats:
        """Get tool usage statistics"""
        try:
            # Build query
            query = {"tenant_id": self.tenant_id}
            if user_id:
                query["user_id"] = user_id
            
            # Add time filter
            since_date = datetime.now() - timedelta(days=days)
            query["timestamp"] = {"$gte": since_date}
            
            # Get basic stats
            total_calls = self.collection.count_documents(query)
            successful_calls = self.collection.count_documents({**query, "success": True})
            failed_calls = total_calls - successful_calls
            
            # Get average execution time
            pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": None,
                    "avg_time": {"$avg": "$execution_time_ms"}
                }}
            ]
            avg_result = list(self.collection.aggregate(pipeline))
            avg_execution_time = avg_result[0]["avg_time"] if avg_result else None
            
            # Get most used tools
            tool_pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": "$tool_name",
                    "count": {"$sum": 1},
                    "success_rate": {"$avg": {"$cond": ["$success", 1, 0]}}
                }},
                {"$sort": {"count": -1}},
                {"$limit": 10}
            ]
            most_used = list(self.collection.aggregate(tool_pipeline))
            
            # Get recent activity
            recent_cursor = self.collection.find(query).sort("timestamp", -1).limit(10)
            recent_activity = []
            for record in recent_cursor:
                recent_activity.append(ToolUsageResponse(
                    id=str(record["_id"]),
                    tool_name=record["tool_name"],
                    tool_description=record["tool_description"],
                    input_data=record.get("input_data", {}),
                    output_data=record.get("output_data", ""),
                    execution_time_ms=record.get("execution_time_ms"),
                    success=record.get("success", True),
                    error_message=record.get("error_message"),
                    timestamp=record["timestamp"],
                    conversation_context=record.get("conversation_context")
                ))
            
            return ToolUsageStats(
                total_calls=total_calls,
                successful_calls=successful_calls,
                failed_calls=failed_calls,
                average_execution_time=avg_execution_time,
                most_used_tools=most_used,
                recent_activity=recent_activity
            )
            
        except Exception as e:
            logger.error(f"❌ Error getting tool usage stats: {e}")
            return ToolUsageStats(
                total_calls=0,
                successful_calls=0,
                failed_calls=0,
                average_execution_time=None,
                most_used_tools=[],
                recent_activity=[]
            )
    
    async def clear_user_tool_usage(self, user_id: str, session_id: Optional[str] = None) -> bool:
        """Clear tool usage records for a user"""
        try:
            query = {"user_id": user_id, "tenant_id": self.tenant_id}
            if session_id:
                query["session_id"] = session_id
            
            result = self.collection.delete_many(query)
            logger.info(f"🗑️ Cleared {result.deleted_count} tool usage records for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error clearing tool usage: {e}")
            return False
