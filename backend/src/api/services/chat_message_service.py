"""
Chat Message Service - Store and retrieve complete chat exchanges with tool usage
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pymongo.collection import Collection
from bson import ObjectId

from core.database import get_db_from_tenant_id
from models.chat_message import (
    ChatMessageModel, ChatMessageCreate, ChatMessageResponse, 
    ChatHistoryResponse, ChatStatsResponse, ToolUsedInMessage
)

logger = logging.getLogger(__name__)


class ChatMessageService:
    """Service for managing chat message storage and retrieval"""
    
    def __init__(self, tenant_id: str):
        """Initialize chat message service"""
        self.tenant_id = tenant_id
        self.db = get_db_from_tenant_id(tenant_id)
        self.collection: Collection = self.db.chat_messages
        
        # Create indexes for better performance
        self._create_indexes()
        
    def _create_indexes(self):
        """Create database indexes for chat messages collection"""
        try:
            # Index for user and session queries
            self.collection.create_index([("user_id", 1), ("session_id", 1)])
            # Index for timestamp queries
            self.collection.create_index([("timestamp", -1)])
            # Compound index for pagination
            self.collection.create_index([("user_id", 1), ("timestamp", -1)])
            
            logger.info("✅ Chat message indexes created")
        except Exception as e:
            logger.warning(f"⚠️ Could not create chat message indexes: {e}")
    
    async def store_chat_exchange(
        self, 
        chat_data: ChatMessageCreate, 
        user_id: str, 
        session_id: str
    ) -> ChatMessageResponse:
        """Store a complete chat exchange (user message + AI response + tools used)"""
        try:
            # Create chat message document
            message_doc = {
                "user_message": chat_data.user_message,
                "ai_response": chat_data.ai_response,
                "tools_used": [tool.dict() for tool in chat_data.tools_used],
                "timestamp": datetime.now(),
                "user_id": user_id,
                "session_id": session_id,
                "tenant_id": self.tenant_id
            }
            
            # Insert into database
            result = self.collection.insert_one(message_doc)
            message_id = str(result.inserted_id)
            
            logger.info(f"✅ Chat exchange stored with ID: {message_id} for user {user_id}")
            
            return ChatMessageResponse(
                id=message_id,
                user_message=chat_data.user_message,
                ai_response=chat_data.ai_response,
                tools_used=chat_data.tools_used,
                timestamp=message_doc["timestamp"]
            )
            
        except Exception as e:
            logger.error(f"❌ Error storing chat exchange: {e}")
            raise
    
    async def get_chat_history(
        self, 
        user_id: str, 
        session_id: Optional[str] = None,
        page: int = 1,
        per_page: int = 50
    ) -> ChatHistoryResponse:
        """Get chat history for a user"""
        try:
            # Build query
            query = {"user_id": user_id, "tenant_id": self.tenant_id}
            if session_id:
                query["session_id"] = session_id
            
            # Calculate skip for pagination
            skip = (page - 1) * per_page
            
            # Get total count
            total_messages = self.collection.count_documents(query)
            
            # Get messages with pagination
            cursor = self.collection.find(query).sort("timestamp", -1).skip(skip).limit(per_page)
            
            messages = []
            for record in cursor:
                # Convert tools_used back to ToolUsedInMessage objects
                tools_used = []
                for tool_data in record.get("tools_used", []):
                    tools_used.append(ToolUsedInMessage(**tool_data))
                
                messages.append(ChatMessageResponse(
                    id=str(record["_id"]),
                    user_message=record["user_message"],
                    ai_response=record["ai_response"],
                    tools_used=tools_used,
                    timestamp=record["timestamp"]
                ))
            
            # Reverse to show oldest first
            messages.reverse()
            
            logger.info(f"📖 Retrieved {len(messages)} chat messages for user {user_id}")
            
            return ChatHistoryResponse(
                session_id=session_id or user_id,
                user_id=user_id,
                messages=messages,
                total_messages=total_messages,
                page=page,
                per_page=per_page
            )
            
        except Exception as e:
            logger.error(f"❌ Error getting chat history: {e}")
            return ChatHistoryResponse(
                session_id=session_id or user_id,
                user_id=user_id,
                messages=[],
                total_messages=0,
                page=page,
                per_page=per_page
            )
    
    async def clear_chat_history(self, user_id: str, session_id: Optional[str] = None) -> bool:
        """Clear chat history for a user"""
        try:
            query = {"user_id": user_id, "tenant_id": self.tenant_id}
            if session_id:
                query["session_id"] = session_id
            
            result = self.collection.delete_many(query)
            logger.info(f"🗑️ Cleared {result.deleted_count} chat messages for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error clearing chat history: {e}")
            return False
    
    async def get_chat_stats(self, user_id: str) -> ChatStatsResponse:
        """Get chat statistics for a user"""
        try:
            # Get total messages
            total_messages = self.collection.count_documents({
                "user_id": user_id, 
                "tenant_id": self.tenant_id
            })
            
            # Get messages from today
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            messages_today = self.collection.count_documents({
                "user_id": user_id,
                "tenant_id": self.tenant_id,
                "timestamp": {"$gte": today_start}
            })
            
            # Get most used tools
            pipeline = [
                {"$match": {"user_id": user_id, "tenant_id": self.tenant_id}},
                {"$unwind": "$tools_used"},
                {"$group": {
                    "_id": "$tools_used.name",
                    "count": {"$sum": 1},
                    "description": {"$first": "$tools_used.description"}
                }},
                {"$sort": {"count": -1}},
                {"$limit": 5}
            ]
            
            most_used_tools = []
            for result in self.collection.aggregate(pipeline):
                most_used_tools.append({
                    "tool_name": result["_id"],
                    "description": result.get("description", ""),
                    "usage_count": result["count"]
                })
            
            # Calculate average tools per message
            pipeline_avg = [
                {"$match": {"user_id": user_id, "tenant_id": self.tenant_id}},
                {"$project": {"tools_count": {"$size": "$tools_used"}}},
                {"$group": {"_id": None, "avg_tools": {"$avg": "$tools_count"}}}
            ]
            
            avg_result = list(self.collection.aggregate(pipeline_avg))
            average_tools_per_message = avg_result[0]["avg_tools"] if avg_result else 0.0
            
            return ChatStatsResponse(
                total_messages=total_messages,
                messages_today=messages_today,
                most_used_tools=most_used_tools,
                average_tools_per_message=round(average_tools_per_message, 2)
            )
            
        except Exception as e:
            logger.error(f"❌ Error getting chat stats: {e}")
            return ChatStatsResponse(
                total_messages=0,
                messages_today=0,
                most_used_tools=[],
                average_tools_per_message=0.0
            )
    
    async def search_chat_history(
        self, 
        user_id: str, 
        search_term: str, 
        limit: int = 20
    ) -> List[ChatMessageResponse]:
        """Search chat history by content"""
        try:
            # Create text search query
            query = {
                "user_id": user_id,
                "tenant_id": self.tenant_id,
                "$or": [
                    {"user_message": {"$regex": search_term, "$options": "i"}},
                    {"ai_response": {"$regex": search_term, "$options": "i"}}
                ]
            }
            
            cursor = self.collection.find(query).sort("timestamp", -1).limit(limit)
            
            messages = []
            for record in cursor:
                # Convert tools_used back to ToolUsedInMessage objects
                tools_used = []
                for tool_data in record.get("tools_used", []):
                    tools_used.append(ToolUsedInMessage(**tool_data))
                
                messages.append(ChatMessageResponse(
                    id=str(record["_id"]),
                    user_message=record["user_message"],
                    ai_response=record["ai_response"],
                    tools_used=tools_used,
                    timestamp=record["timestamp"]
                ))
            
            logger.info(f"🔍 Found {len(messages)} messages matching '{search_term}'")
            return messages
            
        except Exception as e:
            logger.error(f"❌ Error searching chat history: {e}")
            return []
