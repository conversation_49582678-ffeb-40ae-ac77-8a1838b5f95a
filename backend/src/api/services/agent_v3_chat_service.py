"""
Agent V3 Chat Service - Uses Main Agent Controller with Three Sub-Agents
"""

import logging
from typing import Optional
from models.user import UserTenantDB
from api.services.agent_v3.main_agent_controller import MainAgentController

logger = logging.getLogger(__name__)


class AgentV3ChatService:
    """
    Chat service using the new Agent V3 architecture with main controller and sub-agents
    """
    
    def __init__(self):
        """Initialize the chat service"""
        self.agent_controllers = {}  # Cache controllers per user
        logger.info("✅ Agent V3 Chat Service initialized")
    
    def _get_agent_controller(self, current_user: UserTenantDB) -> MainAgentController:
        """Get or create agent controller for user"""
        try:
            user_key = f"{current_user.tenant_id}_{current_user.user.id}"
            
            if user_key not in self.agent_controllers:
                self.agent_controllers[user_key] = MainAgentController(current_user)
                logger.info(f"✅ Created new agent controller for user: {user_key}")
            
            return self.agent_controllers[user_key]
            
        except Exception as e:
            logger.error(f"Error getting agent controller: {e}")
            # Return a new controller as fallback
            return MainAgentController(current_user)
    
    def chat(self, message: str, current_user: UserTenantDB, thread_id: str = None) -> dict:
        """
        Process chat message using Agent V3 architecture
        
        Args:
            message: User's message
            current_user: Current user context
            thread_id: Optional thread ID for conversation tracking
            
        Returns:
            Dict with response, tools_used, and agent information
        """
        try:
            logger.info(f"🚀 Agent V3 Chat processing message: {message}")
            
            # Get agent controller for this user
            agent_controller = self._get_agent_controller(current_user)
            
            # Process the message through the main agent controller
            result = agent_controller.chat(message, thread_id)
            
            # Ensure we have the required fields
            if "response" not in result:
                result["response"] = "I'm here to help! How can I assist you today?"
            
            if "tools_used" not in result:
                result["tools_used"] = []
            
            # Add agent architecture information
            result["architecture"] = "agent_v3"
            result["main_controller"] = "active"
            
            logger.info(f"✅ Agent V3 Chat completed - Agent used: {result.get('agent_used', 'unknown')}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error in Agent V3 Chat: {e}")
            return {
                "response": "I'm here to help! How can I assist you today?",
                "tools_used": [],
                "architecture": "agent_v3",
                "error": str(e)
            }
    
    def get_agent_status(self, current_user: UserTenantDB) -> dict:
        """Get status of the agent architecture for a user"""
        try:
            agent_controller = self._get_agent_controller(current_user)
            return agent_controller.get_agent_status()
            
        except Exception as e:
            logger.error(f"Error getting agent status: {e}")
            return {
                "error": str(e),
                "architecture": "agent_v3"
            }
    
    def clear_user_cache(self, current_user: UserTenantDB):
        """Clear cached agent controller for user"""
        try:
            user_key = f"{current_user.tenant_id}_{current_user.user.id}"
            if user_key in self.agent_controllers:
                del self.agent_controllers[user_key]
                logger.info(f"✅ Cleared agent controller cache for user: {user_key}")
                
        except Exception as e:
            logger.error(f"Error clearing user cache: {e}")
    
    def get_service_info(self) -> dict:
        """Get information about the chat service"""
        return {
            "service_name": "Agent V3 Chat Service",
            "architecture": "main_controller_with_sub_agents",
            "sub_agents": [
                "Information Retrieval Agent",
                "Product Management Agent", 
                "CTA Agent"
            ],
            "features": [
                "Dynamic intent classification",
                "User profile management",
                "Ticket creation and management",
                "Memory persistence",
                "No hardcoding - fully LLM-driven"
            ],
            "cached_controllers": len(self.agent_controllers)
        }
