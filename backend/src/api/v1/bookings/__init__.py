from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

from core.security import get_tenant_info
from models.user import UserTenantDB
from models.booking import BookingCreate, BookingResponse
from api.services.booking_service import BookingService
from utils import setup_colored_logging, log_info, log_error, log_success

# Setup logging
setup_colored_logging()

router = APIRouter(tags=["Bookings"])


class BookingCreateRequest(BaseModel):
    user_name: str
    user_email: str
    user_phone: str
    course_name: str
    course_code: str
    time_slot: str


class BookingUpdateRequest(BaseModel):
    status: str  # confirmed, cancelled, pending


class BookingStatsResponse(BaseModel):
    total_bookings: int
    status_breakdown: Dict[str, int]
    recent_bookings_7_days: int


@router.post("/", response_model=BookingResponse)
async def create_booking(
    booking_request: BookingCreateRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create a new booking
    """
    try:
        user_id = str(current_user.user.id)
        thread_id = user_id  # Using user_id as thread_id for permanent sessions
        
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Create booking data
        booking_data = BookingCreate(
            user_name=booking_request.user_name,
            user_email=booking_request.user_email,
            user_phone=booking_request.user_phone,
            course_name=booking_request.course_name,
            course_code=booking_request.course_code,
            time_slot=booking_request.time_slot
        )
        
        # Create booking
        booking_response = await booking_service.create_booking(
            booking_data, user_id, thread_id
        )
        
        log_success(f"Booking created for user {user_id}: {booking_request.course_name}")
        return booking_response
        
    except Exception as e:
        log_error(f"Error creating booking: {e}")
        raise HTTPException(status_code=500, detail=f"Error creating booking: {str(e)}")


@router.get("/", response_model=List[BookingResponse])
async def get_user_bookings(
    limit: int = Query(50, description="Maximum number of bookings to return"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get all bookings for the current user
    """
    try:
        user_id = str(current_user.user.id)
        
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Get user bookings
        bookings = await booking_service.get_user_booking_history(user_id, limit)
        
        log_info(f"Retrieved {len(bookings)} bookings for user {user_id}")
        return bookings
        
    except Exception as e:
        log_error(f"Error getting user bookings: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving bookings: {str(e)}")


@router.get("/{booking_id}", response_model=BookingResponse)
async def get_booking(
    booking_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a specific booking by ID
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Get booking
        booking = await booking_service.get_booking_by_id(booking_id)
        
        if not booking:
            raise HTTPException(status_code=404, detail="Booking not found")
        
        log_info(f"Retrieved booking {booking_id}")
        return booking
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting booking: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving booking: {str(e)}")


@router.put("/{booking_id}", response_model=Dict[str, Any])
async def update_booking(
    booking_id: str,
    booking_update: BookingUpdateRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update booking status
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Update booking status
        success = await booking_service.update_booking_status(booking_id, booking_update.status)
        
        if success:
            log_success(f"Updated booking {booking_id} status to {booking_update.status}")
            return {
                "success": True,
                "message": f"Booking status updated to {booking_update.status}",
                "booking_id": booking_id
            }
        else:
            raise HTTPException(status_code=404, detail="Booking not found")
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error updating booking: {e}")
        raise HTTPException(status_code=500, detail=f"Error updating booking: {str(e)}")


@router.delete("/{booking_id}", response_model=Dict[str, Any])
async def cancel_booking(
    booking_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Cancel a booking (sets status to cancelled)
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Cancel booking
        success = await booking_service.cancel_booking(booking_id)
        
        if success:
            log_success(f"Cancelled booking {booking_id}")
            return {
                "success": True,
                "message": "Booking cancelled successfully",
                "booking_id": booking_id
            }
        else:
            raise HTTPException(status_code=404, detail="Booking not found")
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error cancelling booking: {e}")
        raise HTTPException(status_code=500, detail=f"Error cancelling booking: {str(e)}")


@router.delete("/{booking_id}/permanent", response_model=Dict[str, Any])
async def delete_booking_permanent(
    booking_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Permanently delete a booking
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Delete booking permanently
        success = await booking_service.delete_booking(booking_id)
        
        if success:
            log_success(f"Permanently deleted booking {booking_id}")
            return {
                "success": True,
                "message": "Booking deleted permanently",
                "booking_id": booking_id
            }
        else:
            raise HTTPException(status_code=404, detail="Booking not found")
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error deleting booking: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting booking: {str(e)}")


@router.get("/status/{status}", response_model=List[BookingResponse])
async def get_bookings_by_status(
    status: str,
    limit: int = Query(100, description="Maximum number of bookings to return"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get bookings by status (confirmed, cancelled, pending)
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Get bookings by status
        bookings = await booking_service.get_bookings_by_status(status, limit)
        
        log_info(f"Retrieved {len(bookings)} bookings with status: {status}")
        return bookings
        
    except Exception as e:
        log_error(f"Error getting bookings by status: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving bookings: {str(e)}")


@router.get("/stats/overview", response_model=BookingStatsResponse)
async def get_booking_stats(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get booking statistics overview
    """
    try:
        # Initialize booking service
        booking_service = BookingService(current_user.tenant_id)
        
        # Get booking stats
        stats = await booking_service.get_booking_stats()
        
        log_info("Retrieved booking statistics")
        return BookingStatsResponse(**stats)
        
    except Exception as e:
        log_error(f"Error getting booking stats: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving booking stats: {str(e)}")
