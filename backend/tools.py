"""
Dynamic Tools for LangMem Memory Management and Booking System
Uses LangMem's memory tools for flexible user information management
"""

import re
from datetime import datetime
from typing import Dict, Any, Optional
from langchain_core.tools import tool
from langmem import create_manage_memory_tool, create_search_memory_tool
from langgraph.store.memory import InMemoryStore

# Initialize LangMem memory store
memory_store = InMemoryStore(
    index={
        "dims": 1536,
        "embed": "openai:text-embedding-3-small",
    }
)

# Create LangMem memory tools with different namespaces
user_memory_tool = create_manage_memory_tool(namespace=("user_info",), store=memory_store)
search_user_memory_tool = create_search_memory_tool(namespace=("user_info",), store=memory_store)
booking_memory_tool = create_manage_memory_tool(namespace=("bookings",), store=memory_store)
search_booking_memory_tool = create_search_memory_tool(namespace=("bookings",), store=memory_store)


class DynamicUserInfoManager:
    """Dynamic user information manager using Lang<PERSON>em"""
    
    def __init__(self):
        self.user_memory = user_memory_tool
        self.search_memory = search_user_memory_tool
        
        # Define extraction patterns dynamically
        self.extraction_patterns = {
            "email": {
                "pattern": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
                "memory_key": "email"
            },
            "phone": {
                "pattern": r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
                "memory_key": "phone",
                "formatter": lambda matches: ''.join(matches[0]) if matches else None
            },
            "name": {
                "patterns": [
                    r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]+?)(?:\s|$|\.|\,)',
                    r'name:\s*([A-Za-z\s]+?)(?:\s|$|\.|\,)'
                ],
                "memory_key": "name",
                "validator": lambda name: len(name.strip()) > 1 and len(name.strip()) < 50
            }
        }
    
    def extract_and_save_info(self, text: str) -> str:
        """Dynamically extract and save user information from text"""
        saved_info = []
        
        for info_type, config in self.extraction_patterns.items():
            if info_type == "name":
                # Handle multiple patterns for names
                for pattern in config["patterns"]:
                    matches = re.findall(pattern, text, re.IGNORECASE)
                    if matches:
                        name = matches[0].strip()
                        if config.get("validator", lambda x: True)(name):
                            self.user_memory.invoke({"content": f"User {config['memory_key']}: {name}"})
                            saved_info.append(f"{info_type}: {name}")
                            break
            else:
                # Handle single pattern extraction
                if "pattern" in config:
                    matches = re.findall(config["pattern"], text)
                    if matches:
                        if "formatter" in config:
                            value = config["formatter"](matches)
                        else:
                            value = matches[0]
                        
                        if value:
                            self.user_memory.invoke({"content": f"User {config['memory_key']}: {value}"})
                            saved_info.append(f"{info_type}: {value}")
        
        return f"💾 Automatically saved: {', '.join(saved_info)}" if saved_info else ""
    
    def get_user_info(self) -> Dict[str, Optional[str]]:
        """Dynamically retrieve user information using LangMem search"""
        user_info = {}
        
        for info_type, config in self.extraction_patterns.items():
            memory_key = config["memory_key"]
            try:
                # Search for this type of information
                search_result = self.search_memory.invoke({"query": f"user {memory_key}"})
                
                if search_result and f"User {memory_key}:" in search_result:
                    # Extract the value using regex
                    pattern = rf'User {memory_key}:\s*([^\n,]+)'
                    match = re.search(pattern, search_result)
                    if match:
                        user_info[memory_key] = match.group(1).strip()
                    else:
                        user_info[memory_key] = None
                else:
                    user_info[memory_key] = None
                    
            except Exception:
                user_info[memory_key] = None
        
        return user_info
    
    def save_specific_info(self, info_type: str, value: str) -> str:
        """Save specific user information"""
        if info_type in self.extraction_patterns:
            memory_key = self.extraction_patterns[info_type]["memory_key"]
            self.user_memory.invoke({"content": f"User {memory_key}: {value}"})
            return f"✅ Saved user {info_type}: {value}"
        else:
            return f"❌ Unknown information type: {info_type}"


# Initialize the dynamic user info manager
user_info_manager = DynamicUserInfoManager()


@tool
def save_user_information(name: str = "", email: str = "", phone: str = "") -> str:
    """
    Save user contact information for future use using LangMem.
    
    Args:
        name: User's full name
        email: User's email address
        phone: User's phone number
    """
    try:
        saved_items = []
        
        if name:
            result = user_info_manager.save_specific_info("name", name)
            saved_items.append("name")
        if email:
            result = user_info_manager.save_specific_info("email", email)
            saved_items.append("email")
        if phone:
            result = user_info_manager.save_specific_info("phone", phone)
            saved_items.append("phone")
            
        if not saved_items:
            return "❌ No information provided to save"
            
        return f"✅ Saved user {', '.join(saved_items)}. I'll remember this information for future bookings!"
        
    except Exception as e:
        return f"❌ Failed to save user information: {str(e)}"


@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """
    Book a service/course for the user. Automatically uses saved user information if available.
    
    Args:
        service_name: Name of the service/course to book
        user_name: User's full name (optional if already saved)
        user_email: User's email address (optional if already saved)
        user_phone: User's phone number (optional if already saved)
    """
    try:
        # Get saved user information if not provided
        saved_info = user_info_manager.get_user_info()
        
        # Use saved info if parameters not provided
        if not user_name and saved_info.get("name"):
            user_name = saved_info["name"]
        if not user_email and saved_info.get("email"):
            user_email = saved_info["email"]
        if not user_phone and saved_info.get("phone"):
            user_phone = saved_info["phone"]
        
        # Validate required information
        missing_info = []
        if not user_name:
            missing_info.append("name")
        if not user_email:
            missing_info.append("email")
        if not user_phone:
            missing_info.append("phone")
            
        if missing_info:
            return f"❌ Cannot complete booking. Missing required information: {', '.join(missing_info)}. Please provide your {', '.join(missing_info)} to proceed with booking."
        
        # Generate booking ID
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # Save booking to memory using LangMem
        booking_info = f"Booking confirmed - ID: {booking_id}, Service: {service_name}, Customer: {user_name}, Email: {user_email}, Phone: {user_phone}, Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        booking_memory_tool.invoke({"content": booking_info})
        
        return f"""✅ Booking Confirmed!
        
📋 Booking Details:
• Booking ID: {booking_id}
• Service: {service_name}
• Customer: {user_name}
• Email: {user_email}
• Phone: {user_phone}
• Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📧 A confirmation email will be sent to {user_email}
📱 You may receive an SMS confirmation at {user_phone}

Thank you for your booking!"""
        
    except Exception as e:
        return f"❌ Booking failed: {str(e)}"


@tool
def get_user_profile() -> str:
    """
    Get the current user's saved profile information.
    """
    try:
        user_info = user_info_manager.get_user_info()
        
        profile_parts = []
        for key, value in user_info.items():
            if value:
                profile_parts.append(f"{key.title()}: {value}")
        
        if profile_parts:
            return f"👤 Your Profile:\n" + "\n".join(f"• {part}" for part in profile_parts)
        else:
            return "👤 No profile information saved yet. Please provide your name, email, and phone number."
            
    except Exception as e:
        return f"❌ Failed to retrieve profile: {str(e)}"


@tool
def search_bookings(query: str = "") -> str:
    """
    Search through previous bookings.
    
    Args:
        query: Search query for bookings (optional)
    """
    try:
        if not query:
            query = "booking confirmed"
        
        results = search_booking_memory_tool.invoke({"query": query})
        
        if results:
            return f"📋 Found bookings:\n{results}"
        else:
            return "📋 No bookings found."
            
    except Exception as e:
        return f"❌ Failed to search bookings: {str(e)}"


# Export all tools for use in the main application
def get_all_tools():
    """Return all available tools"""
    return [
        save_user_information,
        book_service,
        get_user_profile,
        search_bookings,
        user_memory_tool,
        search_user_memory_tool,
        booking_memory_tool,
        search_booking_memory_tool
    ]


# Export the user info manager for external use
def get_user_info_manager():
    """Return the user info manager instance"""
    return user_info_manager
