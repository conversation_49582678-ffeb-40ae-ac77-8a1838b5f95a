{"cells": [{"cell_type": "code", "execution_count": 12, "id": "b76936e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎓 Nepali EdTech AI Assistant\n", "(Type 'exit' to quit)\n", "\n", "AI: content='नमस्ते दिवस! तपाईं AI र वेब विकासमा चासो राख्नुहुन्छ भनी थाहा पाउँदा खुशी लाग्यो। म तपाईंलाई यी विषयहरूमा कुनै पनि प्रश्नहरूमा सहयोग गर्न तयार छु। के तपाईंलाई कुनै विशेष कुरा जान्नुछ वा कुन प्रकारका कोर्सहरू लिन मन छ?' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 70, 'prompt_tokens': 452, 'total_tokens': 522, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw0Qerfy0xbN1xH5IMol9LWmaK0jy', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None} id='run--c5cd91d6-9544-4fb7-8e83-88024c232759-0' usage_metadata={'input_tokens': 452, 'output_tokens': 70, 'total_tokens': 522, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}\n", "\n", "AI: content='दिवस, तपाईंको रूचि अनुसार एउटा राम्रो कोर्स सुझाव दिन्छु: \\n\\nAI कोर्स जसमा तपाईंले Machine Learning, Deep Learning, र Natural Language Processing (NLP) सिक्न सक्नुहुन्छ। यो कोर्सले तपाईंलाई AI को आधारदेखि प्रगतिशील स्तरसम्म ज्ञान दिनेछ।\\n\\nयदि वेब विकासमा विशेष कोर्स चाहनु हुन्छ भने पनि भन्नुहोस्, म सजिलै सुझाव दिन सक्छु।' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 96, 'prompt_tokens': 580, 'total_tokens': 676, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw0QzMi6INOxQkvA994J4jBekpix3', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None} id='run--72e09296-7b23-43bb-948e-73b871c5f754-0' usage_metadata={'input_tokens': 580, 'output_tokens': 96, 'total_tokens': 676, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}\n", "\n", "👋 Goodbye!\n"]}], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.store.memory import InMemoryStore\n", "from langgraph.utils.config import get_store\n", "from langmem import create_manage_memory_tool, create_memory_manager\n", "from pydantic import BaseModel\n", "from typing import Optional\n", "\n", "# ✅ 1. Define user profile schema\n", "class UserProfile(BaseModel):\n", "    name: Optional[str] = None\n", "    language: Optional[str] = None\n", "    timezone: Optional[str] = None\n", "    age: Optional[int] = None\n", "    academic_level: Optional[str] = None\n", "    interests: Optional[list[dict]] = None\n", "    career_goals: Optional[list[dict]] = None\n", "\n", "# ✅ 2. Memory extraction manager\n", "manager = create_memory_manager(\n", "    \"openai:gpt-4.1-mini\",\n", "    schemas=[UserProfile],\n", "    instructions=\"Extract user profile (interests, academic level, goals, etc.) from conversation.\",\n", "    enable_inserts=False,\n", ")\n", "\n", "# ✅ 3. Dummy retriever tool\n", "def retriever_tool(query: str) -> str:\n", "    \"\"\"Simulated retriever for courses\"\"\"\n", "    knowledge_base = {\n", "        \"ai\": \"AI Course: Learn Machine Learning, Deep Learning, and NLP.\",\n", "        \"web dev\": \"Web Development Bootcamp: HTML, CSS, JS, React, and Django.\",\n", "        \"data science\": \"Data Science Specialization: Python, Pandas, ML models.\"\n", "    }\n", "    for key, val in knowledge_base.items():\n", "        if key in query.lower():\n", "            return val\n", "    return \"I couldn’t find an exact match. You can explore AI, Web Dev, or Data Science courses.\"\n", "\n", "# ✅ 4. Prompt that merges memories\n", "def prompt(state):\n", "    store = get_store()\n", "    memories = store.search((\"memories\",), query=state[\"messages\"][-1].content)\n", "\n", "    system_msg = f\"\"\"\n", "You are a helpful AI assistant for Nepali students.\n", "\n", "- Always use stored user profile info if available.\n", "- If user asks for courses, check their interests first.\n", "- If no relevant memory, retrieve general course suggestions.\n", "\n", "## Stored Memories:\n", "{memories}\n", "\"\"\"\n", "    return [{\"role\": \"system\", \"content\": system_msg}, *state[\"messages\"]]\n", "\n", "# ✅ 5. Setup memory store & checkpoint\n", "store = InMemoryStore(\n", "    index={\n", "        \"dims\": 1536,\n", "        \"embed\": \"openai:text-embedding-3-small\",\n", "    }\n", ")\n", "checkpointer = MemorySaver()\n", "\n", "# ✅ 6. Create the agent with memory & retriever tools\n", "agent = create_react_agent(\n", "    \"openai:gpt-4.1-mini\",\n", "    prompt=prompt,\n", "    tools=[\n", "        create_manage_memory_tool(namespace=(\"memories\",)),\n", "        retriever_tool,  # Q&A from retriever\n", "    ],\n", "    store=store,\n", "    checkpointer=checkpointer,\n", ")\n", "\n", "# ✅ 7. Simple chat loop\n", "if __name__ == \"__main__\":\n", "    print(\"🎓 Nepali EdTech AI Assistant\\n(Type 'exit' to quit)\\n\")\n", "    new_config={\"configurable\": {\"thread_id\": \"thread-b\"}}\n", "    while True:\n", "        user_input = input(\"You: \")\n", "        if user_input.lower() in [\"exit\", \"quit\"]:\n", "            print(\"👋 Goodbye!\")\n", "            break\n", "\n", "        # Send input to agent\n", "        result = agent.invoke(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": user_input}]},\n", "    config=new_config,\n", ")\n", "        \n", "        # Print AI response\n", "        ai_reply = result[\"messages\"][-1]\n", "        print(f\"AI: {ai_reply}\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a87ee63b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nam<PERSON>e! How can I help you with your studies or career plans today?\n"]}], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "bf0fd7dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Hello', additional_kwargs={}, response_metadata={}, id='55ad0810-5132-4ccf-8994-a2cfffc43f7d'),\n", "  AIMessage(content='Namaste! How can I help you with your studies or career plans today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 17, 'prompt_tokens': 381, 'total_tokens': 398, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw09eFjXlDbqzWZ4mhtv5T3HGs8oq', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--e7f5005f-e343-4f9d-b65f-465da33335f3-0', usage_metadata={'input_tokens': 381, 'output_tokens': 17, 'total_tokens': 398, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}),\n", "  HumanMessage(content='I jsut completed My SEE exams and looking to study science. I am interested in engineering and want to know about the best colleges in Nepal for engineering.', additional_kwargs={}, response_metadata={}, id='c37b5582-6836-4927-9c8c-a079b0abb185'),\n", "  AIMessage(content='Congratulations on completing your SEE exams! Choosing science and aiming for engineering is a great path.\\n\\nHere are some of the best engineering colleges in Nepal you can consider:\\n\\n1. **Pulchowk Campus, IOE (Tribhuvan University)**  \\n   - One of the top and most competitive engineering campuses in Nepal.  \\n   - Offers Bachelor in Engineering (BE) in various fields like Civil, Mechanical, Electrical, and more.  \\n   - Admission through the IOE entrance exam.\\n\\n2. **Thapathali Campus, IOE (Tribhuvan University)**  \\n   - Known for Computer and Electronics engineering programs.  \\n   - Also requires passing the IOE entrance exam.\\n\\n3. **Patan Multiple Campus, TU**  \\n   - Offers engineering courses with good faculty and facilities.\\n\\n4. **Kathmandu University (KU)**  \\n   - Private university with engineering programs in fields like Computer, Electrical, Civil, and Mechanical Engineering.  \\n   - Well-known for quality education and research.\\n\\n5. **Nepal Engineering College (NEC)**  \\n   - Private college near Kathmandu with several engineering programs.\\n\\n6. **Kantipur Engineering College**  \\n   - Offers different engineering streams and modern facilities.\\n\\nTo join these colleges, you usually need to pass an entrance exam after class 12 (plus two). Since you just completed SEE, you will first complete your +2 science, usually with Physics, Chemistry, and Math, to be eligible for engineering.\\n\\nIf you want, I can also guide you on which +2 colleges are good for science, or tips to prepare for engineering entrance exams. Would you like that?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 329, 'prompt_tokens': 436, 'total_tokens': 765, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw0AQd1XCvx9CmKf31kVNM0aRTQH2', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--6a99a92b-78cf-410c-8794-e58b7f7411b0-0', usage_metadata={'input_tokens': 436, 'output_tokens': 329, 'total_tokens': 765, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.invoke(\n", "{\n", "        \"messages\": [\n", "            {\"role\": \"user\", \"content\": \"I jsut completed My SEE exams and looking to study science. I am interested in engineering and want to know about the best colleges in Nepal for engineering.\"}\n", "        ]\n", "    },\n", "    # We will continue the conversation (thread-a) by using the config with\n", "    # the same thread_id\n", "    config=config,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "3697e8f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Hello', additional_kwargs={}, response_metadata={}, id='55ad0810-5132-4ccf-8994-a2cfffc43f7d'),\n", "  AIMessage(content='Namaste! How can I help you with your studies or career plans today?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 17, 'prompt_tokens': 381, 'total_tokens': 398, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw09eFjXlDbqzWZ4mhtv5T3HGs8oq', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--e7f5005f-e343-4f9d-b65f-465da33335f3-0', usage_metadata={'input_tokens': 381, 'output_tokens': 17, 'total_tokens': 398, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}),\n", "  HumanMessage(content='I jsut completed My SEE exams and looking to study science. I am interested in engineering and want to know about the best colleges in Nepal for engineering.', additional_kwargs={}, response_metadata={}, id='c37b5582-6836-4927-9c8c-a079b0abb185'),\n", "  AIMessage(content='Congratulations on completing your SEE exams! Choosing science and aiming for engineering is a great path.\\n\\nHere are some of the best engineering colleges in Nepal you can consider:\\n\\n1. **Pulchowk Campus, IOE (Tribhuvan University)**  \\n   - One of the top and most competitive engineering campuses in Nepal.  \\n   - Offers Bachelor in Engineering (BE) in various fields like Civil, Mechanical, Electrical, and more.  \\n   - Admission through the IOE entrance exam.\\n\\n2. **Thapathali Campus, IOE (Tribhuvan University)**  \\n   - Known for Computer and Electronics engineering programs.  \\n   - Also requires passing the IOE entrance exam.\\n\\n3. **Patan Multiple Campus, TU**  \\n   - Offers engineering courses with good faculty and facilities.\\n\\n4. **Kathmandu University (KU)**  \\n   - Private university with engineering programs in fields like Computer, Electrical, Civil, and Mechanical Engineering.  \\n   - Well-known for quality education and research.\\n\\n5. **Nepal Engineering College (NEC)**  \\n   - Private college near Kathmandu with several engineering programs.\\n\\n6. **Kantipur Engineering College**  \\n   - Offers different engineering streams and modern facilities.\\n\\nTo join these colleges, you usually need to pass an entrance exam after class 12 (plus two). Since you just completed SEE, you will first complete your +2 science, usually with Physics, Chemistry, and Math, to be eligible for engineering.\\n\\nIf you want, I can also guide you on which +2 colleges are good for science, or tips to prepare for engineering entrance exams. Would you like that?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 329, 'prompt_tokens': 436, 'total_tokens': 765, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw0AQd1XCvx9CmKf31kVNM0aRTQH2', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--6a99a92b-78cf-410c-8794-e58b7f7411b0-0', usage_metadata={'input_tokens': 436, 'output_tokens': 329, 'total_tokens': 765, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}),\n", "  HumanMessage(content='yes but i would need some preprartions for the entrance exams. Can you help me with that?', additional_kwargs={}, response_metadata={}, id='34d5a153-2ea1-4fb3-8177-edcbcdf42659'),\n", "  AIMessage(content='Absolutely! Preparing for engineering entrance exams in Nepal needs good planning and practice. Here are some tips to help you get ready:\\n\\n1. **Understand the Syllabus**  \\n   - Get the syllabus for the IOE entrance exam or other relevant exams you want to take.  \\n   - Usually, it covers Physics, Chemistry, Mathematics, and sometimes English.\\n\\n2. **Create a Study Schedule**  \\n   - Dedicate daily time for each subject.  \\n   - Balance between concepts, practice questions, and revision.\\n\\n3. **Focus on Concepts**  \\n   - Understand basic concepts clearly; don’t just memorize.  \\n   - Engineering entrance questions test your understanding and application.\\n\\n4. **Practice Past Papers**  \\n   - Solve previous years’ entrance exam papers.  \\n   - This helps you know the question pattern and time management.\\n\\n5. **Use Quality Reference Books**  \\n   - NCERT books (Class 11 and 12) are very helpful for basics.  \\n   - You can also use entrance exam preparation books available in Nepal.\\n\\n6. **Take Online Classes or Coaching**  \\n   - Join online courses specialized for IOE or other engineering entrances if needed.  \\n   - Many Nepali coaching centers offer crash courses and mock tests.\\n\\n7. **Join Study Groups**  \\n   - Discuss problems and concepts with friends or online groups.  \\n   - This keeps you motivated and clears doubts.\\n\\n8. **Keep Healthy and Positive**  \\n   - Take breaks, eat well, and sleep enough.  \\n   - Stay confident and don’t stress too much.\\n\\nIf you want, I can recommend some online resources or free classes tailored for Nepal’s engineering entrance exams. Would you like that?', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 342, 'prompt_tokens': 794, 'total_tokens': 1136, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4.1-mini-2025-04-14', 'system_fingerprint': None, 'id': 'chatcmpl-Bw0BI8ZEXtfiA18NwqLGGAxZ2zJi9', 'service_tier': 'default', 'finish_reason': 'stop', 'logprobs': None}, id='run--053cb73f-3408-46a3-b2ce-9cd2f3784b28-0', usage_metadata={'input_tokens': 794, 'output_tokens': 342, 'total_tokens': 1136, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["agent.invoke(\n", "{\n", "        \"messages\": [\n", "            {\"role\": \"user\", \"content\": \"yes but i would need some preprartions for the entrance exams. Can you help me with that?\"}\n", "        ]\n", "    },\n", "    # We will continue the conversation (thread-a) by using the config with\n", "    # the same thread_id\n", "    config=config,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "6ff781ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "test-agent", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}