from retreival import get_today_date,qna_search, product_search
import os
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

tools = [get_today_date, qna_search, product_search]

# Load ReAct prompt

# Initialize ChatOpenAI model with proper configuration
# model = ChatOpenAI(
#     model="gpt-4o-mini",  # Use model parameter instead of name
#     temperature=0.1,
#     api_key=OPENAI_API_KEY,
# )

model = init_chat_model(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Create the prompt template with required placeholders
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant that can answer questions using the available tools.

When a user asks a question, you should:

1. Analyze the user's query to determine the intent
2. Use the appropriate tool based on the query type:
   - For questions about products, courses, or what's available → use product_search
   - For technical issues, troubleshooting, or app problems → use qna_search
   - For date/time requests → use get_today_date
   - For simple greetings (hi, hello, hey) → respond directly without using tools

3. Always use tools for non-greeting queries - do not answer from your own knowledge

Examples:
- "what products do you have?" → use product_search
- "my app crashed" → use qna_search
- "what's today's date?" → use get_today_date
- "hello" → respond with a greeting directly"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Create the agent with MongoDB memory
def get_session_history(session_id: str) -> MongoDBChatMessageHistory:
    """
    Get or create MongoDB chat message history for a session
    This function creates a new MongoDBChatMessageHistory instance for each session
    """
    return MongoDBChatMessageHistory(
        connection_string=os.getenv("MONGO_URI", "mongodb://localhost:27017/"),
        database_name="test_agent_db",
        collection_name="chat_history",
        session_id=session_id
    )

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("Chat application started with MongoDB memory. Type 'exit' or 'quit' to stop.")
print("Available commands:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Ask for today's date")
print("- Your conversation history will be stored in MongoDB")

# Use a default session ID for this test
session_id = "test-session-001"

while True:
    try:
        user_input = input("\nUser: ")
        if user_input.lower() in ["exit", "quit"]:
            print("Exiting the chat.")
            break

        # Execute the agent with the user input and session configuration
        print("Processing your request...")
        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        # Print the response from the agent
        print(f"\nAgent: {response['output']}")

    except KeyboardInterrupt:
        print("\nExiting the chat.")
        break
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")