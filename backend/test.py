from retreival import get_today_date, qna_search, product_search
from tools import get_all_tools, get_user_info_manager
import os
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON><PERSON>plate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Initialize LangMem memory store
memory_store = InMemoryStore(
    index={
        "dims": 1536,
        "embed": "openai:text-embedding-3-small",
    }
)

# Create LangMem memory tools
user_memory_tool = create_manage_memory_tool(namespace=("user_info",), store=memory_store)
search_user_memory_tool = create_search_memory_tool(namespace=("user_info",), store=memory_store)

def extract_and_save_user_info(text: str) -> str:
    """Extract user info from text and save using LangMem tools"""
    saved_info = []

    # Extract email
    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    emails = re.findall(email_pattern, text)
    if emails:
        user_memory_tool.invoke({"content": f"User email: {emails[0]}"})
        saved_info.append(f"email: {emails[0]}")

    # Extract phone (simple pattern for various formats)
    phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
    phones = re.findall(phone_pattern, text)
    if phones:
        phone = ''.join(phones[0])
        user_memory_tool.invoke({"content": f"User phone: {phone}"})
        saved_info.append(f"phone: {phone}")

    # Extract name (look for "my name is" or "I'm" patterns)
    name_patterns = [
        r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]+?)(?:\s|$|\.|\,)',
        r'name:\s*([A-Za-z\s]+?)(?:\s|$|\.|\,)'
    ]
    for pattern in name_patterns:
        names = re.findall(pattern, text, re.IGNORECASE)
        if names:
            name = names[0].strip()
            if len(name) > 1 and len(name) < 50:  # Basic validation
                user_memory_tool.invoke({"content": f"User name: {name}"})
                saved_info.append(f"name: {name}")
                break

    if saved_info:
        return f"💾 Automatically saved: {', '.join(saved_info)}"
    return ""

def get_user_info() -> dict:
    """Get user information using LangMem search"""
    try:
        # Search for user info
        name_results = search_user_memory_tool.invoke({"query": "user name"})
        email_results = search_user_memory_tool.invoke({"query": "user email"})
        phone_results = search_user_memory_tool.invoke({"query": "user phone"})

        user_info = {"name": None, "email": None, "phone": None}

        # Extract name
        if name_results and "User name:" in name_results:
            name_match = re.search(r'User name:\s*([^,\n]+)', name_results)
            if name_match:
                user_info["name"] = name_match.group(1).strip()

        # Extract email
        if email_results and "User email:" in email_results:
            email_match = re.search(r'User email:\s*([^\s,\n]+)', email_results)
            if email_match:
                user_info["email"] = email_match.group(1).strip()

        # Extract phone
        if phone_results and "User phone:" in phone_results:
            phone_match = re.search(r'User phone:\s*([^\s,\n]+)', phone_results)
            if phone_match:
                user_info["phone"] = phone_match.group(1).strip()

        return user_info
    except Exception as e:
        return {"name": None, "email": None, "phone": None}

# Booking tool using LangMem
@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """
    Book a service/course for the user. Automatically uses saved user information if available.

    Args:
        service_name: Name of the service/course to book
        user_name: User's full name (optional if already saved)
        user_email: User's email address (optional if already saved)
        user_phone: User's phone number (optional if already saved)
    """
    try:
        # Get saved user information if not provided
        saved_info = get_user_info()

        # Use saved info if parameters not provided
        if not user_name and saved_info.get("name"):
            user_name = saved_info["name"]
        if not user_email and saved_info.get("email"):
            user_email = saved_info["email"]
        if not user_phone and saved_info.get("phone"):
            user_phone = saved_info["phone"]

        # Validate required information
        missing_info = []
        if not user_name:
            missing_info.append("name")
        if not user_email:
            missing_info.append("email")
        if not user_phone:
            missing_info.append("phone")

        if missing_info:
            return f"❌ Cannot complete booking. Missing required information: {', '.join(missing_info)}. Please provide your {', '.join(missing_info)} to proceed with booking."

        # Simulate booking process
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # Save booking to memory
        booking_info = f"Booking confirmed - ID: {booking_id}, Service: {service_name}, Customer: {user_name}, Email: {user_email}, Phone: {user_phone}, Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        user_memory_tool.invoke({"content": booking_info})

        return f"""✅ Booking Confirmed!

📋 Booking Details:
• Booking ID: {booking_id}
• Service: {service_name}
• Customer: {user_name}
• Email: {user_email}
• Phone: {user_phone}
• Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📧 A confirmation email will be sent to {user_email}
📱 You may receive an SMS confirmation at {user_phone}

Thank you for your booking!"""

    except Exception as e:
        return f"❌ Booking failed: {str(e)}"

@tool
def save_user_information(name: str = "", email: str = "", phone: str = "") -> str:
    """
    Save user contact information for future use using LangMem.

    Args:
        name: User's full name
        email: User's email address
        phone: User's phone number
    """
    try:
        saved_items = []
        if name:
            user_memory_tool.invoke({"content": f"User name: {name}"})
            saved_items.append("name")
        if email:
            user_memory_tool.invoke({"content": f"User email: {email}"})
            saved_items.append("email")
        if phone:
            user_memory_tool.invoke({"content": f"User phone: {phone}"})
            saved_items.append("phone")

        if not saved_items:
            return "❌ No information provided to save"

        return f"✅ Saved user {', '.join(saved_items)}. I'll remember this information for future bookings!"

    except Exception as e:
        return f"❌ Failed to save user information: {str(e)}"

# Add LangMem memory tools to the tools list
tools = [
    get_today_date,
    qna_search,
    product_search,
    book_service,
    save_user_information,
    user_memory_tool,
    search_user_memory_tool
]



model = init_chat_model(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Create the prompt template with required placeholders
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with persistent memory and booking capabilities using LangMem.

MEMORY CAPABILITIES (LangMem + MongoDB approach):
- You remember previous conversations with users across sessions (MongoDB chat history)
- You automatically save user information using LangMem memory tools
- You can search and retrieve user information for personalized responses
- Your memory is stored persistently using LangMem's memory store

USER INFORMATION MANAGEMENT (LangMem):
- Use manage_memory tool to save user contact information
- Use search_memory tool to retrieve saved user information
- Automatically detect and save user details when mentioned
- Use saved information for bookings without asking again

AVAILABLE TOOLS:
1. product_search - Search for available courses/products
2. qna_search - Search for technical support and troubleshooting
3. get_today_date - Get current date/time
4. book_service - Book a service/course (automatically uses saved user info)
5. save_user_information - Manually save user contact details
6. manage_memory - LangMem tool to create/update memories
7. search_memory - LangMem tool to search stored memories

TOOL USAGE LOGIC:
- For product inquiries → use product_search
- For technical issues → use qna_search
- For booking requests → use book_service (automatically retrieves saved user info)
- For date/time → use get_today_date
- For saving contact info → use save_user_information or manage_memory
- For retrieving user info → use search_memory

BOOKING PROCESS:
1. When user wants to book something, book_service automatically checks for saved user info
2. If missing name, email, or phone - ask for the missing information
3. Use save_user_information to store provided details
4. Use book_service with all required information
5. Confirm booking details with the user

USER INFO DETECTION:
- When user provides personal info, use manage_memory to save it
- Save as: "User name: John Smith", "User email: <EMAIL>", "User phone: ************"
- Use search_memory to retrieve user information when needed

MEMORY USAGE:
- Reference previous conversations for context
- Use search_memory to find saved user information
- Remember user preferences and interests using manage_memory
- Acknowledge when you remember something from earlier

Examples:
- "what courses do you have?" → use product_search
- "I want to book the German course" → use book_service (auto-retrieves user info)
- "my name is John Smith" → use manage_memory to save
- "my app crashed" → use qna_search
- "what's my email?" → use search_memory to find saved email"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Enhanced Memory Management with LangMem + MongoDB Chat History
class MemoryManager:
    """Memory manager combining LangMem for user info and MongoDB for chat history"""

    def __init__(self):
        self.connection_string = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = "test_agent_db"
        self._session_histories = {}

    def get_session_history(self, session_id: str) -> MongoDBChatMessageHistory:
        """
        Get or create MongoDB chat message history for a session
        Implements persistent memory as described in the LangChain article
        """
        if session_id not in self._session_histories:
            self._session_histories[session_id] = MongoDBChatMessageHistory(
                connection_string=self.connection_string,
                database_name=self.database_name,
                collection_name="chat_history",
                session_id=session_id
            )
            print(f"📝 Created MongoDB chat history for session: {session_id}")

        return self._session_histories[session_id]

    def get_session_summary(self, session_id: str) -> dict:
        """Get conversation summary for a session"""
        try:
            history = self.get_session_history(session_id)
            messages = history.messages
            user_info = get_user_info()  # Use LangMem function

            return {
                "session_id": session_id,
                "message_count": len(messages),
                "has_history": len(messages) > 0,
                "user_info": user_info
            }
        except Exception as e:
            return {"error": str(e)}

    def process_user_input(self, session_id: str, user_input: str) -> str:
        """Process user input and extract any user information using LangMem"""
        return extract_and_save_user_info(user_input)

# Initialize memory manager
memory_manager = MemoryManager()

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    memory_manager.get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("🧠 Enhanced Chat with MongoDB Memory + Booking System")
print("=" * 70)
print("Available features:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Book services/courses (requires name, email, phone)")
print("- Automatic user information detection and saving")
print("- Type 'summary' to see session summary and saved user info")
print("- Type 'exit' or 'quit' to stop")
print("- Your conversation history is persistently stored in MongoDB!")
print("=" * 70)

# Use a default session ID for this test
session_id = "enhanced-session-001"
current_session_id = session_id  # Global variable for tools
print(f"📋 Session ID: {session_id}")

# Show existing history and user info if any
summary = memory_manager.get_session_summary(session_id)
if summary.get("has_history"):
    print(f"📚 Found existing conversation history ({summary['message_count']} messages)")
else:
    print("🆕 Starting fresh conversation")

# Show saved user info
user_info = summary.get("user_info", {})
saved_info = [k for k, v in user_info.items() if v]
if saved_info:
    print(f"👤 Saved user info: {', '.join(saved_info)}")
else:
    print("👤 No user information saved yet")

while True:
    try:
        user_input = input(f"\n[{session_id[-8:]}] User: ")

        if user_input.lower() in ["exit", "quit"]:
            print("👋 Exiting the chat. Your conversation and user info are saved in MongoDB!")
            break
        elif user_input.lower() == "summary":
            summary = memory_manager.get_session_summary(session_id)
            print(f"\n📊 Session Summary:")
            print(f"   Messages: {summary.get('message_count', 0)}")
            user_info = summary.get('user_info', {})
            print(f"   User Info: {user_info}")
            continue

        # Automatically extract and save user information from input
        auto_save_result = memory_manager.process_user_input(session_id, user_input)
        if auto_save_result:
            print(auto_save_result)

        # Execute the agent with the user input and session configuration
        print("🤔 Processing your request with memory context...")

        # Get current user info to help with bookings
        current_user_info = memory_manager.user_memory.get_user_info(session_id)

        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        # Print the response from the agent
        print(f"\n🤖 Agent: {response['output']}")

        # Show memory status
        updated_summary = memory_manager.get_session_summary(session_id)
        user_info = updated_summary.get('user_info', {})
        saved_info = [k for k, v in user_info.items() if v]

        print(f"💾 Memory: {updated_summary['message_count']} messages stored")
        if saved_info:
            print(f"👤 User info: {', '.join(saved_info)} saved")

    except KeyboardInterrupt:
        print("\n👋 Exiting the chat. Your conversation and user info are saved in MongoDB!")
        break
    except Exception as e:
        print(f"\n❌ Error occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")