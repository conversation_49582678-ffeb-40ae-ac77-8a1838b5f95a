from retreival import get_today_date,qna_search, product_search
import os
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

tools = [get_today_date, qna_search, product_search]



model = init_chat_model(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Create the prompt template with required placeholders
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with persistent memory that can answer questions using available tools.

MEMORY CAPABILITIES (following <PERSON><PERSON><PERSON><PERSON> article patterns):
- You remember previous conversations with users across sessions
- You can reference past interactions and build context over time
- You maintain conversation continuity and user preferences
- Your memory is stored persistently in MongoDB

TOOL USAGE:
1. Analyze the user's query to determine the intent
2. Use the appropriate tool based on the query type:
   - For questions about products, courses, or what's available → use product_search
   - For technical issues, troubleshooting, or app problems → use qna_search
   - For date/time requests → use get_today_date
   - For simple greetings (hi, hello, hey) → respond directly without using tools

3. Always use tools for non-greeting queries - do not answer from your own knowledge
4. Reference previous conversations when relevant to provide better context

MEMORY USAGE:
- If the user refers to "that product" or "the course I asked about", check chat history
- Remember user preferences and interests from previous conversations
- Build on previous interactions to provide more personalized responses
- Acknowledge when you remember something from earlier in the conversation

Examples:
- "what products do you have?" → use product_search
- "my app crashed" → use qna_search
- "what's today's date?" → use get_today_date
- "hello" → respond with a greeting, mention if you remember them
- "tell me more about that course" → check chat history for context, then use product_search"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Enhanced MongoDB Memory Management (following LangChain article patterns)
class MemoryManager:
    """MongoDB memory manager following the article's best practices"""

    def __init__(self):
        self.connection_string = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = "test_agent_db"
        self._session_histories = {}

    def get_session_history(self, session_id: str) -> MongoDBChatMessageHistory:
        """
        Get or create MongoDB chat message history for a session
        Implements persistent memory as described in the LangChain article
        """
        if session_id not in self._session_histories:
            self._session_histories[session_id] = MongoDBChatMessageHistory(
                connection_string=self.connection_string,
                database_name=self.database_name,
                collection_name="chat_history",
                session_id=session_id
            )
            print(f"📝 Created MongoDB chat history for session: {session_id}")

        return self._session_histories[session_id]

    def get_session_summary(self, session_id: str) -> dict:
        """Get conversation summary for a session"""
        try:
            history = self.get_session_history(session_id)
            messages = history.messages
            return {
                "session_id": session_id,
                "message_count": len(messages),
                "has_history": len(messages) > 0
            }
        except Exception as e:
            return {"error": str(e)}

# Initialize memory manager
memory_manager = MemoryManager()

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    memory_manager.get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("🧠 Enhanced Chat with MongoDB Memory (LangChain Article Implementation)")
print("=" * 70)
print("Available commands:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Ask for today's date")
print("- Type 'summary' to see session summary")
print("- Type 'exit' or 'quit' to stop")
print("- Your conversation history is persistently stored in MongoDB!")
print("=" * 70)

# Use a default session ID for this test
session_id = "enhanced-session-001"
print(f"📋 Session ID: {session_id}")

# Show existing history if any
summary = memory_manager.get_session_summary(session_id)
if summary.get("has_history"):
    print(f"📚 Found existing conversation history ({summary['message_count']} messages)")
else:
    print("🆕 Starting fresh conversation")

while True:
    try:
        user_input = input(f"\n[{session_id[-8:]}] User: ")

        if user_input.lower() in ["exit", "quit"]:
            print("👋 Exiting the chat. Your conversation is saved in MongoDB!")
            break
        elif user_input.lower() == "summary":
            summary = memory_manager.get_session_summary(session_id)
            print(f"\n📊 Session Summary: {summary}")
            continue

        # Execute the agent with the user input and session configuration
        print("🤔 Processing your request with memory context...")
        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        # Print the response from the agent
        print(f"\n🤖 Agent: {response['output']}")

        # Show memory status
        updated_summary = memory_manager.get_session_summary(session_id)
        print(f"💾 Memory: {updated_summary['message_count']} messages stored")

    except KeyboardInterrupt:
        print("\n👋 Exiting the chat. Your conversation is saved in MongoDB!")
        break
    except Exception as e:
        print(f"\n❌ Error occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")