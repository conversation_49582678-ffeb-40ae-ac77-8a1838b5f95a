from retreival import get_today_date,qna_search, product_search
import os
import re
import json
from datetime import datetime
from dotenv import load_dotenv

from langchain.agents import create_tool_calling_agent, AgentExecutor
from langchain.chat_models import init_chat_model
from langchain_core.runnables.history import RunnableWithMessageHistory
from langchain_core.prompts import ChatPrompt<PERSON><PERSON>plate
from langchain_core.tools import tool
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory
from pymongo import MongoClient
load_dotenv(override=True)

# Verify OpenAI API key is loaded
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# User Memory System for storing user information
class UserMemory:
    """Stores and manages user information (name, email, phone) in MongoDB"""

    def __init__(self, connection_string: str, database_name: str):
        self.client = MongoClient(connection_string)
        self.db = self.client[database_name]
        self.users_collection = self.db.user_profiles

    def save_user_info(self, session_id: str, name: str = None, email: str = None, phone: str = None):
        """Save or update user information"""
        user_data = {"session_id": session_id, "updated_at": datetime.now()}

        if name:
            user_data["name"] = name
        if email:
            user_data["email"] = email
        if phone:
            user_data["phone"] = phone

        # Update existing or create new
        self.users_collection.update_one(
            {"session_id": session_id},
            {"$set": user_data},
            upsert=True
        )
        return f"✅ Saved user info: {', '.join([k for k, v in user_data.items() if v and k not in ['session_id', 'updated_at']])}"

    def get_user_info(self, session_id: str) -> dict:
        """Get user information for a session"""
        user = self.users_collection.find_one({"session_id": session_id})
        if user:
            return {
                "name": user.get("name"),
                "email": user.get("email"),
                "phone": user.get("phone")
            }
        return {"name": None, "email": None, "phone": None}

    def extract_and_save_from_text(self, session_id: str, text: str) -> str:
        """Extract user info from text and save it"""
        saved_info = []

        # Extract email
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        if emails:
            self.save_user_info(session_id, email=emails[0])
            saved_info.append(f"email: {emails[0]}")

        # Extract phone (simple pattern for various formats)
        phone_pattern = r'(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phones = re.findall(phone_pattern, text)
        if phones:
            phone = ''.join(phones[0])
            self.save_user_info(session_id, phone=phone)
            saved_info.append(f"phone: {phone}")

        # Extract name (look for "my name is" or "I'm" patterns)
        name_patterns = [
            r'(?:my name is|i am|i\'m|call me)\s+([A-Za-z\s]+?)(?:\s|$|\.|\,)',
            r'name:\s*([A-Za-z\s]+?)(?:\s|$|\.|\,)'
        ]
        for pattern in name_patterns:
            names = re.findall(pattern, text, re.IGNORECASE)
            if names:
                name = names[0].strip()
                if len(name) > 1 and len(name) < 50:  # Basic validation
                    self.save_user_info(session_id, name=name)
                    saved_info.append(f"name: {name}")
                    break

        if saved_info:
            return f"💾 Automatically saved: {', '.join(saved_info)}"
        return ""

# Initialize user memory
user_memory = None  # Will be initialized with memory manager

# Booking tool
@tool
def book_service(service_name: str, user_name: str = "", user_email: str = "", user_phone: str = "") -> str:
    """
    Book a service/course for the user. Automatically uses saved user information if available.

    Args:
        service_name: Name of the service/course to book
        user_name: User's full name (optional if already saved)
        user_email: User's email address (optional if already saved)
        user_phone: User's phone number (optional if already saved)
    """
    global user_memory, current_session_id

    try:
        # Get saved user information if not provided
        if user_memory and current_session_id:
            saved_info = user_memory.get_user_info(current_session_id)

            # Use saved info if parameters not provided
            if not user_name and saved_info.get("name"):
                user_name = saved_info["name"]
            if not user_email and saved_info.get("email"):
                user_email = saved_info["email"]
            if not user_phone and saved_info.get("phone"):
                user_phone = saved_info["phone"]

        # Validate required information
        missing_info = []
        if not user_name:
            missing_info.append("name")
        if not user_email:
            missing_info.append("email")
        if not user_phone:
            missing_info.append("phone")

        if missing_info:
            return f"❌ Cannot complete booking. Missing required information: {', '.join(missing_info)}. Please provide your {', '.join(missing_info)} to proceed with booking."

        # Simulate booking process
        booking_id = f"BK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # In a real system, this would save to a bookings database
        booking_data = {
            "booking_id": booking_id,
            "service": service_name,
            "customer_name": user_name,
            "customer_email": user_email,
            "customer_phone": user_phone,
            "booking_date": datetime.now().isoformat(),
            "status": "confirmed"
        }

        return f"""✅ Booking Confirmed!

📋 Booking Details:
• Booking ID: {booking_id}
• Service: {service_name}
• Customer: {user_name}
• Email: {user_email}
• Phone: {user_phone}
• Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📧 A confirmation email will be sent to {user_email}
📱 You may receive an SMS confirmation at {user_phone}

Thank you for your booking!"""

    except Exception as e:
        return f"❌ Booking failed: {str(e)}"

@tool
def save_user_information(name: str = "", email: str = "", phone: str = "") -> str:
    """
    Save user contact information for future use.

    Args:
        name: User's full name
        email: User's email address
        phone: User's phone number
    """
    global user_memory, current_session_id

    if not user_memory:
        return "❌ User memory not initialized"

    try:
        saved_items = []
        if name:
            saved_items.append("name")
        if email:
            saved_items.append("email")
        if phone:
            saved_items.append("phone")

        if not saved_items:
            return "❌ No information provided to save"

        result = user_memory.save_user_info(current_session_id, name, email, phone)
        return f"{result}. I'll remember this information for future bookings!"

    except Exception as e:
        return f"❌ Failed to save user information: {str(e)}"

tools = [get_today_date, qna_search, product_search, book_service, save_user_information]



model = init_chat_model(
    model="gpt-4o-mini",  # Use model parameter instead of name
    temperature=0.1,
    api_key=OPENAI_API_KEY,
)


# Create the prompt template with required placeholders
prompt = ChatPromptTemplate.from_messages([
    ("system", """You are a helpful assistant with persistent memory and booking capabilities.

MEMORY CAPABILITIES (following LangChain article patterns):
- You remember previous conversations with users across sessions
- You automatically save user information (name, email, phone) when mentioned
- You can reference past interactions and build context over time
- Your memory is stored persistently in MongoDB

USER INFORMATION MANAGEMENT:
- Automatically detect and save user contact information when mentioned
- Remember user details for future bookings and interactions
- If user provides name, email, or phone - save it automatically
- Use saved information for bookings without asking again

AVAILABLE TOOLS:
1. product_search - Search for available courses/products
2. qna_search - Search for technical support and troubleshooting
3. get_today_date - Get current date/time
4. book_service - Book a service/course (requires name, email, phone)
5. save_user_information - Manually save user contact details

TOOL USAGE LOGIC:
- For product inquiries → use product_search
- For technical issues → use qna_search
- For booking requests → use book_service (check if user info is available first)
- For date/time → use get_today_date
- For saving contact info → use save_user_information

BOOKING PROCESS:
1. When user wants to book something, first check if you have their contact info
2. If missing name, email, or phone - ask for the missing information
3. Use book_service tool with all required information
4. Confirm booking details with the user

USER INFO DETECTION:
- Automatically save when user says "My name is John", "I'm Sarah", "Call me Mike"
- Save emails when mentioned: "my <NAME_EMAIL>"
- Save phone numbers when provided: "my phone is ************"
- Use save_user_information tool to store this data

MEMORY USAGE:
- Reference previous conversations for context
- Use saved user information for personalized responses
- Remember user preferences and interests
- Acknowledge when you remember something from earlier

Examples:
- "what courses do you have?" → use product_search
- "I want to book the German course" → check user info, then use book_service
- "my name is John Smith" → use save_user_information
- "my app crashed" → use qna_search
- "book that course we discussed" → check chat history + use book_service"""),
    ("placeholder", "{chat_history}"),
    ("human", "{input}"),
    ("placeholder", "{agent_scratchpad}"),
])

# Enhanced MongoDB Memory Management (following LangChain article patterns)
class MemoryManager:
    """MongoDB memory manager following the article's best practices"""

    def __init__(self):
        self.connection_string = os.getenv("MONGO_URI", "mongodb://localhost:27017/")
        self.database_name = "test_agent_db"
        self._session_histories = {}

        # Initialize user memory
        self.user_memory = UserMemory(self.connection_string, self.database_name)

    def get_session_history(self, session_id: str) -> MongoDBChatMessageHistory:
        """
        Get or create MongoDB chat message history for a session
        Implements persistent memory as described in the LangChain article
        """
        if session_id not in self._session_histories:
            self._session_histories[session_id] = MongoDBChatMessageHistory(
                connection_string=self.connection_string,
                database_name=self.database_name,
                collection_name="chat_history",
                session_id=session_id
            )
            print(f"📝 Created MongoDB chat history for session: {session_id}")

        return self._session_histories[session_id]

    def get_session_summary(self, session_id: str) -> dict:
        """Get conversation summary for a session"""
        try:
            history = self.get_session_history(session_id)
            messages = history.messages
            user_info = self.user_memory.get_user_info(session_id)

            return {
                "session_id": session_id,
                "message_count": len(messages),
                "has_history": len(messages) > 0,
                "user_info": user_info
            }
        except Exception as e:
            return {"error": str(e)}

    def process_user_input(self, session_id: str, user_input: str) -> str:
        """Process user input and extract any user information"""
        return self.user_memory.extract_and_save_from_text(session_id, user_input)

# Initialize memory manager
memory_manager = MemoryManager()
user_memory = memory_manager.user_memory  # Global reference for tools

agent = create_tool_calling_agent(model, tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=tools, verbose=True)

# Wrap agent with MongoDB-backed chat history
agent_with_chat_history = RunnableWithMessageHistory(
    agent_executor,
    memory_manager.get_session_history,  # Function that returns MongoDB chat history
    input_messages_key="input",
    history_messages_key="chat_history",
)

print("🧠 Enhanced Chat with MongoDB Memory + Booking System")
print("=" * 70)
print("Available features:")
print("- Ask questions about products (will use Product Search)")
print("- Ask technical questions (will use QNA Search)")
print("- Book services/courses (requires name, email, phone)")
print("- Automatic user information detection and saving")
print("- Type 'summary' to see session summary and saved user info")
print("- Type 'exit' or 'quit' to stop")
print("- Your conversation history is persistently stored in MongoDB!")
print("=" * 70)

# Use a default session ID for this test
session_id = "enhanced-session-001"
current_session_id = session_id  # Global variable for tools
print(f"📋 Session ID: {session_id}")

# Show existing history and user info if any
summary = memory_manager.get_session_summary(session_id)
if summary.get("has_history"):
    print(f"📚 Found existing conversation history ({summary['message_count']} messages)")
else:
    print("🆕 Starting fresh conversation")

# Show saved user info
user_info = summary.get("user_info", {})
saved_info = [k for k, v in user_info.items() if v]
if saved_info:
    print(f"👤 Saved user info: {', '.join(saved_info)}")
else:
    print("👤 No user information saved yet")

while True:
    try:
        user_input = input(f"\n[{session_id[-8:]}] User: ")

        if user_input.lower() in ["exit", "quit"]:
            print("👋 Exiting the chat. Your conversation and user info are saved in MongoDB!")
            break
        elif user_input.lower() == "summary":
            summary = memory_manager.get_session_summary(session_id)
            print(f"\n📊 Session Summary:")
            print(f"   Messages: {summary.get('message_count', 0)}")
            user_info = summary.get('user_info', {})
            print(f"   User Info: {user_info}")
            continue

        # Automatically extract and save user information from input
        auto_save_result = memory_manager.process_user_input(session_id, user_input)
        if auto_save_result:
            print(auto_save_result)

        # Execute the agent with the user input and session configuration
        print("🤔 Processing your request with memory context...")

        # Get current user info to help with bookings
        current_user_info = memory_manager.user_memory.get_user_info(session_id)

        response = agent_with_chat_history.invoke(
            {"input": user_input},
            config={"configurable": {"session_id": session_id}}
        )

        # Print the response from the agent
        print(f"\n🤖 Agent: {response['output']}")

        # Show memory status
        updated_summary = memory_manager.get_session_summary(session_id)
        user_info = updated_summary.get('user_info', {})
        saved_info = [k for k, v in user_info.items() if v]

        print(f"💾 Memory: {updated_summary['message_count']} messages stored")
        if saved_info:
            print(f"👤 User info: {', '.join(saved_info)} saved")

    except KeyboardInterrupt:
        print("\n👋 Exiting the chat. Your conversation and user info are saved in MongoDB!")
        break
    except Exception as e:
        print(f"\n❌ Error occurred: {str(e)}")
        print("Please try again or type 'exit' to quit.")